
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width">
	<title>Cute Crushies - Blockchain Game on the WAX Network</title><link href="images/ui/logos/cc-logo-25.png" rel="icon" type="image/png">
	<link href="./css/animations.css" rel="stylesheet">
	<link href="./css/style.css" rel="stylesheet" type="text/css" />
	<link href="./css/tiles.css" rel="stylesheet">
	<link href="./css/info-modal.css" rel="stylesheet">
  <link href="./css/fx.css" rel="stylesheet">

  <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
  <link href="https://fonts.cdnfonts.com/css/common-pixel" rel="stylesheet">
  <link href="https://fonts.cdnfonts.com/css/ttvtechprecomput" rel="stylesheet">
  <link href="https://fonts.cdnfonts.com/css/commodore-64-pixelized" rel="stylesheet">
  <script src="../utils/waxjs.js"></script>
   <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <link href="https://cdn.jsdelivr.net/npm/uikit@3.9.3/dist/css/uikit.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

  <script src="./audio/sound.js"></script>

</head>
<body>
  <div id="map-editor-container" class="card">
  <h2>Map Editor</h2>
  <form>
    <fieldset>
      <div>
        <!-- <h3>Edit Tiles</h3> -->
        <div class="map-editor-text-display">
            <p id="map-editor-selection"><strong>Selected Area:</strong> No tiles selected</p>
            <p id="mapEditorDisplayText"><strong>Display Text:</strong> No changes proposed</p>
          </div>
        <div class="form-check">
          <input id="mapEditorActivateCheckbox" class="form-check-input" type="checkbox" onchange="toggleMapEditor()" checked="false">
          <label class="form-check-label" for="mapEditorActivateCheckbox">Enable Map Editor</label>
        </div>
      </div>
      <div>
        <label for="mapEditorLocaleName">Location Name:</label>
        <input id="mapEditorLocaleName" class="form-control" type="text" placeholder="Enter location name" onchange="updateMapEditorSettings()">
      </div>
      <div>
        <select id="tileOptionsWorld0" class="form-select" onchange="updateMapEditorSettings()">
          <option>grassplains</option>
          <option>water</option>
          <option>forest</option>
          <option>castle</option>
          <option>town</option>
          <option>ruins</option>
        </select>
      </div>
      <div>
        <select id="tileOptionsWorld1" class="form-select" onchange="updateMapEditorSettings()">
          <option>tr_water</option>
          <option>tr_island</option>
          <option>tr_castle</option>
          <option>tr_waterland</option>
        </select>
      </div>
      <div>
        <select id="tileOptionsWorld2" class="form-select" onchange="updateMapEditorSettings()">
          <option>ds_dirt</option>
          <option>ds_dunes</option>
          <option>ds_ruins</option>
          <option>ds_town</option>
          <option>ds_castle</option>
        </select>
      </div>
      <div>
        <select id="tileOptionsWorld3" class="form-select" onchange="updateMapEditorSettings()">
          <option>sp_normal</option>
          <option>sp_debris</option>
          <option>sp_station1</option>
          <option>sp_gas1</option>
          <option>sp_gplanet1</option>
          <option>sp_dplanet1</option>
          <option>sp_iplanet1</option>
          <option>sp_rplanet1</option>
        </select>
      </div>
      <div>
        <label for="mapEditorTileTypeLand">Tile Type:</label>
        <div class="form-check">
          <input id="mapEditorTileTypeLand" class="form-check-input" name="tileType" type="radio" value="land" checked onchange="updateMapEditorSettings()">
          <label class="form-check-label" for="mapEditorTileTypeLand">Land</label>
        </div>
        <div class="form-check">
          <input id="mapEditorTileTypeWater" class="form-check-input" name="tileType" type="radio" value="water" onchange="updateMapEditorSettings()">
          <label class="form-check-label" for="mapEditorTileTypeWater">Water</label>
        </div>
        <div class="form-check">
          <input id="mapEditorTileTypeSpace" class="form-check-input" name="tileType" type="radio" value="space" onchange="updateMapEditorSettings()">
          <label class="form-check-label" for="mapEditorTileTypeSpace">Space</label>
        </div>
      </div>
      <div>
        <div class="form-check">
          <input id="mapEditorRandomNamesCheckbox" class="form-check-input" type="checkbox" onchange="updateMapEditorSettings()">
          <label class="form-check-label" for="mapEditorRandomNamesCheckbox">Random Locale Names</label>
        </div>
      </div>
      <div class="mt-2">
    <a href="#" onclick="appendEditsToZone()" class="btn btn-primary" title="Submit all changes to the zone">
      <img src="images/editor/MPE_SaveIcon.png"> Update
    </a>
    <a href="#" onclick="clearMapEditorQueue()" class="btn btn-secondary" title="Clear all changes to the zone">
      <img src="images/editor/MPE_RemoveIcon.png"> Cancel
    </a>
    <a href="#" onclick="reloadEditorMap()" class="btn btn-secondary" title="Clear all changes to the zone">
      <img src="images/editor/MPE_ReloadIcon.png"> Reload Map
    </a>
    <!-- <a href="#" onclick="reloadEditorView()" class="btn btn-secondary">
      <img src="images/editor/MPE_ReloadIcon.png"> Reload
    </a> -->
  </div>
    </fieldset>
  </form>
  <hr>
  <h3>Create New Zone</h3>
  <div>
      <p id="mapEditorZoneSelected"><strong>Selected Zone:</strong></p>
      <a href="#"  class="btn btn-secondary" onclick="generateLocale(nav.zone)" title="Create 256 new locales for the selected zone">
        <img src="images/editor/MPE_AddIcon.png">Create New Zone
      </a>
    </div>
</div>


	<div class="container-fluid">
	  <div class="row">
	    <div class="col-md-12 justify-content-between">
      <div class="row">

        <div  id="column-1" class="col-md-3 mt-3">
          <p>Column 1: No content in this column needed.</p>
        </div>

        <div id="column-3" class="col-md-4 mt-3">
            <p>Column 1: No content in this column needed.</p>
        </div>

				<div id="column-2" class="col-md-5 mt-3">

			  <div class="card" id="map-bg">
			    <div class="d-flex btn-group world_nav_container">
			      <button id="world_btn" class="world_nav_buttons active" onclick="displayWorlds();">Overworld</button>
			      <button id="zone_btn" class="world_nav_buttons" onclick="displayZones(nav.world);">Zones</button>
			      <button id="locale_btn" class="world_nav_buttons" onclick="displayLocales(nav.zone);">Locales</button>
						<div id="tooltip"></div>
			    </div>

			    <div class="container-fluid" style="width: 546px;">
			        <div id="map-border"><div class="map shadow"></div></div>
			    </div>
			  </div>

			</div>

      </div>

    </div>
  </div>
</div>

<div id="main-content"></div>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
   <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.3/dist/umd/popper.min.js"></script>
   <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

<script src="script.js"></script>
<script src="map.js"></script>
<script>

// document.getElementById('updateButton').addEventListener('click', async () => {
//      const world = nav.world; // Get the world from somewhere
//      const zone = nav.zone; // Get the zone from somewhere
//
//      const localeName = document.getElementById('localeName').value;
//      const terrain = document.getElementById('terrain').value;
//      const tileType = document.getElementById('tileType').value;
//
//      const jdata = [{
//        "Tile": tileType,
//        "Locale": 0,
//        "Terrain": terrain,
//        "Locale_Name": localeName
//      }];
//
//      try {
//        const response = await updateMapZoneData(world, zone, jdata);
//        console.log('Success:', response.data);
//      } catch (error) {
//        console.error('Error:', error);
//      }
//    });


   document.addEventListener('click', function(event) {
     var moreLink = document.querySelector('#navbarDropdown');
     var dropdownMenu = document.querySelector('.dropdown-menu');
     var targetElement = event.target;

     if (targetElement === moreLink || dropdownMenu.contains(targetElement)) {
       dropdownMenu.classList.toggle('show');
     } else {
       dropdownMenu.classList.remove('show');
     }
   });


   async function updateMapZoneData(world, zone, jdata) {
     const url = `${domain_url}/players/zones/updatedata/${world}/${zone}`;
     const change = {
       mapgrid_4: world,
       mapgrid_16: zone,
       data: jdata
     };

     try {
       const response = await axios.put(url, change, {
         headers: {
           'Content-Type': 'application/json'
         }
       });
       return response.data;
     } catch (error) {
       throw error;
     }
   }


		const wax = new waxjs.WaxJS({
				rpcEndpoint: 'https://wax.greymass.com'
		});


</script>

<div id="main-alert" class="alert alert-primary" role="alert" style="display:none"></div>
<div id="main-info" class="alert alert-primary" role="alert" style="display:none"></div>
<div id="info-modal"></div>

</body>
</html>
